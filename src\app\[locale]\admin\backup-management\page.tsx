
"use client";

import React, { useState, useEffect } from 'react';
import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { DatabaseBackup, Loader2, CheckCircle, RotateCcw } from 'lucide-react';
import { useScopedI18n } from '@/lib/i18n/client';
import { useToast } from '@/hooks/use-toast';
import { getMockProducts } from '@/lib/mock-product-data';

const DEBTS_STORAGE_KEY = 'marketSyncOwnerDebts'; 
const LAST_BACKUP_TIMESTAMP_KEY = 'marketSyncLastBackupTimestamp';
const LAST_BACKUP_SUMMARY_KEY = 'marketSyncLastBackupSummary';

export default function BackupManagementPage() {
  const t = useScopedI18n('backupManagement');
  const tCommon = useScopedI18n('common');
  const { toast } = useToast();
  const [isLoadingBackup, setIsLoadingBackup] = useState(false);
  const [isLoadingRestore, setIsLoadingRestore] = useState(false);
  const [lastBackup, setLastBackup] = useState<string | null>(null);
  const [backupSummary, setBackupSummary] = useState<string | null>(null);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const storedTimestamp = localStorage.getItem(LAST_BACKUP_TIMESTAMP_KEY);
      if (storedTimestamp) {
        setLastBackup(new Date(storedTimestamp).toLocaleString());
      }
      const storedSummary = localStorage.getItem(LAST_BACKUP_SUMMARY_KEY);
      if (storedSummary) {
        setBackupSummary(storedSummary);
      }
    }
  }, []);

  const handleCreateBackup = () => {
    if (typeof window === 'undefined') {
      toast({
        title: tCommon('error'),
        description: "Cannot perform backup outside browser environment.",
        variant: 'destructive',
      });
      return;
    }

    setIsLoadingBackup(true);
    toast({ title: t('backupInProgress') });

    setTimeout(() => {
      try {
        // Simulate fetching data
        const allProducts = getMockProducts();
        const ownerProducts = allProducts.filter(p => p.ownerId);
        const wholesalerProducts = allProducts.filter(p => p.wholesalerId);
        
        let ownerDebtsCount = 0;
        const storedDebts = localStorage.getItem(DEBTS_STORAGE_KEY);
        if (storedDebts) {
            const allDebts = JSON.parse(storedDebts);
            ownerDebtsCount = allDebts.filter((d: any) => d.ownerId).length;
        }

        const totalProductsBackedUp = ownerProducts.length + wholesalerProducts.length;
        
        const currentTimestamp = new Date();
        localStorage.setItem(LAST_BACKUP_TIMESTAMP_KEY, currentTimestamp.toISOString());
        
        const summaryMsg = t('backupSummary', {productCount: totalProductsBackedUp.toString(), debtCount: ownerDebtsCount.toString() });
        localStorage.setItem(LAST_BACKUP_SUMMARY_KEY, summaryMsg);

        setLastBackup(currentTimestamp.toLocaleString());
        setBackupSummary(summaryMsg);

        toast({
          title: tCommon('success'),
          description: t('backupSuccessful', { timestamp: currentTimestamp.toLocaleString() }),
          action: <CheckCircle className="h-5 w-5 text-green-500" />,
        });
      } catch (error) {
        console.error("Backup error:", error);
        toast({
          title: tCommon('error'),
          description: t('backupFailed'),
          variant: 'destructive',
        });
      } finally {
        setIsLoadingBackup(false);
      }
    }, 1500); 
  };

  const handleRestoreBackup = () => {
     if (typeof window === 'undefined') {
      toast({
        title: tCommon('error'),
        description: "Cannot perform restore outside browser environment.",
        variant: 'destructive',
      });
      return;
    }
    setIsLoadingRestore(true);
    toast({ title: t('restoreInProgress')});

    setTimeout(() => {
        const storedTimestamp = localStorage.getItem(LAST_BACKUP_TIMESTAMP_KEY);
        if (storedTimestamp) {
            // Simulate restore logic - e.g., re-initializing some mock data or just showing success
            toast({
                title: t('restoreSuccessfulTitle'),
                description: t('restoreSuccessfulDesc', { timestamp: new Date(storedTimestamp).toLocaleString() }),
                action: <CheckCircle className="h-5 w-5 text-green-500" />,
            });
        } else {
             toast({
                title: t('restoreFailedTitle'),
                description: t('noBackupToRestore'),
                variant: 'destructive',
            });
        }
        setIsLoadingRestore(false);
    }, 1500);
  }

  return (
    <AuthenticatedLayout expectedRole="admin">
      <div className="space-y-6 p-1">
        <div className="flex items-center space-x-3">
          <DatabaseBackup className="h-8 w-8 text-primary" />
          <div>
            <h1 className="text-3xl font-bold text-foreground">{t('title')}</h1>
            <p className="text-muted-foreground">
              {t('description')}
            </p>
          </div>
        </div>

        <div className="grid md:grid-cols-2 gap-6">
            <Card className="shadow-lg">
            <CardHeader>
                <CardTitle>{t('createBackup')}</CardTitle>
                <CardDescription>
                {t('backupDataFor')}
                </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
                <Button onClick={handleCreateBackup} disabled={isLoadingBackup} className="w-full md:w-auto bg-accent hover:bg-accent/90 text-accent-foreground">
                {isLoadingBackup ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                    <DatabaseBackup className="mr-2 h-4 w-4" />
                )}
                {isLoadingBackup ? t('backupInProgress') : t('backupNow')}
                </Button>
                <p className="text-xs text-muted-foreground">{t('backupDataNote')}</p>
                 <div className="mt-4">
                    <p className="text-sm">
                        <span className="font-medium">{t('lastBackupTime')}:</span>{' '}
                        {lastBackup ? (
                        <span className="text-primary">{lastBackup}</span>
                        ) : (
                        <span className="italic">{t('noBackupPerformed')}</span>
                        )}
                    </p>
                    {backupSummary && (
                        <p className="text-xs text-muted-foreground">{backupSummary}</p>
                    )}
                </div>
            </CardContent>
            </Card>

            <Card className="shadow-lg">
                <CardHeader>
                    <CardTitle>{t('restoreOperationsTitle')}</CardTitle>
                    <CardDescription>{t('restoreOperationsDesc')}</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    <Button onClick={handleRestoreBackup} disabled={isLoadingRestore || !lastBackup} className="w-full md:w-auto">
                        {isLoadingRestore ? (
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        ) : (
                            <RotateCcw className="mr-2 h-4 w-4" />
                        )}
                        {isLoadingRestore ? t('restoreInProgress') : t('restoreButton')}
                    </Button>
                    <p className="text-xs text-muted-foreground">
                        {lastBackup ? t('restoreWarning') : t('noBackupToRestoreNote')}
                    </p>
                </CardContent>
            </Card>
        </div>

      </div>
    </AuthenticatedLayout>
  );
}

