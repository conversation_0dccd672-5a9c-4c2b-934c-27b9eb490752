
"use client";

import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Bell } from 'lucide-react';
import { useScopedI18n } from '@/lib/i18n/client';

export default function EmployeeNotificationsPage() {
  const t = useScopedI18n('common'); // Using common scope for placeholder

  return (
    <AuthenticatedLayout expectedRole="employee">
      <div className="space-y-6 p-1">
        <div className="flex items-center space-x-3">
          <Bell className="h-8 w-8 text-primary" />
          <div>
            <h1 className="text-3xl font-bold text-foreground">{t('nav_notifications')}</h1>
            <p className="text-muted-foreground">
              {/* TODO: Add specific description translation key */}
              View important updates and alerts. This feature is under development.
            </p>
          </div>
        </div>

        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle>{t('nav_notifications')}</CardTitle>
            <CardDescription>
              {/* TODO: Add specific description translation key */}
              This section will display relevant notifications for employees.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground text-center py-6">
              {/* TODO: Add specific message translation key */}
              Notification system will be implemented here.
            </p>
          </CardContent>
        </Card>
      </div>
    </AuthenticatedLayout>
  );
}
