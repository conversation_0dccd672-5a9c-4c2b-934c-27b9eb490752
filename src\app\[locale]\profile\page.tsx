"use client";

import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { UserCog, Save } from 'lucide-react';
import type { Role } from '@/types';
import { useAuth } from '@/contexts/auth-context'; 
import { useScopedI18n } from '@/lib/i18n/client';

const allRoles: Role[] = ['admin', 'owner', 'employee', 'customer', 'wholesaler', 'agent'];

export default function ProfilePage() {
  const { user } = useAuth(); 
  const tProfilePage = useScopedI18n('profile');
  const tUserManagement = useScopedI18n('userManagement');
  const tCommon = useScopedI18n('common');


  const getInitials = (name?: string) => {
    if (!name) return "U";
    const parts = name.split(" ");
    if (parts.length > 1) {
      return `${parts[0][0]}${parts[parts.length - 1][0]}`.toUpperCase();
    }
    return name.substring(0, 2).toUpperCase();
  };

  return (
    <AuthenticatedLayout expectedRole={allRoles}>
      <div className="space-y-6 p-1">
        <div className="flex items-center space-x-3">
          <UserCog className="h-8 w-8 text-primary" />
          <div>
            <h1 className="text-3xl font-bold text-foreground">{tProfilePage('title')}</h1> 
            <p className="text-muted-foreground">
              {tProfilePage('description')}
            </p>
          </div>
        </div>

        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle>{tProfilePage('detailsTitle')}</CardTitle>
            <CardDescription>
              {tProfilePage('detailsDescription', {role: user?.role ? tUserManagement(`role_${user.role}` as any, undefined, user.role) : tCommon('N_A')})}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center space-x-4">
              <Avatar className="h-20 w-20">
                <AvatarImage src={`https://i.pravatar.cc/150?u=${user?.id || 'default'}`} alt={user?.name || user?.username} />
                <AvatarFallback>{getInitials(user?.name || user?.username)}</AvatarFallback>
              </Avatar>
              <div>
                <h2 className="text-xl font-semibold">{user?.name || user?.username}</h2>
                <p className="text-sm text-muted-foreground">{user?.email}</p>
              </div>
            </div>

            <form className="space-y-4">
              <div>
                <Label htmlFor="fullName">{tUserManagement('name')}</Label>
                <Input id="fullName" defaultValue={user?.name || ''} placeholder={tUserManagement('name')} />
              </div>
              <div>
                <Label htmlFor="username">{tUserManagement('username')}</Label>
                <Input id="username" defaultValue={user?.username || ''} disabled />
                 <p className="text-xs text-muted-foreground mt-1">{tProfilePage('usernameCannotBeChanged')}</p>
              </div>
              <div>
                <Label htmlFor="email">{tUserManagement('email')}</Label>
                <Input id="email" type="email" defaultValue={user?.email || ''} placeholder={tUserManagement('email')} />
              </div>
              
              <Button type="submit" className="bg-accent hover:bg-accent/90 text-accent-foreground">
                <Save className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {tProfilePage('updateProfileButton')}
              </Button>
            </form>
          </CardContent>
        </Card>
        
        <Card className="shadow-md">
          <CardHeader>
            <CardTitle>{tProfilePage('changePasswordTitle')}</CardTitle>
            <CardDescription>{tProfilePage('changePasswordDescription')}</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
             <form className="space-y-4">
                <div>
                    <Label htmlFor="currentPassword">{tProfilePage('currentPasswordLabel')}</Label>
                    <Input id="currentPassword" type="password" placeholder={tProfilePage('currentPasswordPlaceholder')} />
                </div>
                <div>
                    <Label htmlFor="newPassword">{tProfilePage('newPasswordLabel')}</Label>
                    <Input id="newPassword" type="password" placeholder={tProfilePage('newPasswordPlaceholder')} />
                </div>
                <div>
                    <Label htmlFor="confirmPassword">{tProfilePage('confirmPasswordLabel')}</Label>
                    <Input id="confirmPassword" type="password" placeholder={tProfilePage('confirmPasswordPlaceholder')} />
                </div>
                <Button type="submit">{tProfilePage('changePasswordButton')}</Button>
             </form>
          </CardContent>
        </Card>
      </div>
    </AuthenticatedLayout>
  );
}
