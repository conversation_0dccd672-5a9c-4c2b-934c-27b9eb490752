"use client";

import { AuthenticatedLayout } from '@/components/layout/authenticated-layout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Settings as SettingsIcon, Bell, Palette, Save } from 'lucide-react';
import type { Role } from '@/types';
import { useTheme } from 'next-themes'; 
import { useScopedI18n } from '@/lib/i18n/client';

const allRoles: Role[] = ['admin', 'owner', 'employee', 'customer', 'wholesaler', 'agent'];

export default function SettingsPage() {
  const { theme, setTheme } = useTheme();
  const tOwnerSettings = useScopedI18n('ownerSettings'); // Using ownerSettings scope as it has relevant keys
  const tCommon = useScopedI18n('common');

  return (
    <AuthenticatedLayout expectedRole={allRoles}>
      <div className="space-y-6 p-1">
        <div className="flex items-center space-x-3">
          <SettingsIcon className="h-8 w-8 text-primary" />
          <div>
            <h1 className="text-3xl font-bold text-foreground">{tCommon('nav_generalSettings')}</h1> 
            <p className="text-muted-foreground">
              {tOwnerSettings('description')} 
            </p>
          </div>
        </div>

        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle>{tOwnerSettings('appearanceSettings')}</CardTitle>
            <CardDescription>
              {tOwnerSettings('appearanceSettingsDescription')}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="theme" className="text-base">{tOwnerSettings('theme')}</Label>
                <p className="text-sm text-muted-foreground">
                  {tOwnerSettings('themeDescription')}
                </p>
              </div>
              <Select value={theme} onValueChange={(value) => setTheme(value)}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder={tOwnerSettings('selectTheme')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="light">{tOwnerSettings('themeLight')}</SelectItem>
                  <SelectItem value="dark">{tOwnerSettings('themeDark')}</SelectItem>
                  <SelectItem value="system">{tOwnerSettings('themeSystem')}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-md">
          <CardHeader>
            <CardTitle>{tOwnerSettings('notificationSettings')}</CardTitle>
            <CardDescription>{tOwnerSettings('notificationSettingsDescription')}</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="emailNotifications" className="font-medium">{tOwnerSettings('emailNotifications')}</Label>
                <p className="text-sm text-muted-foreground">{tOwnerSettings('emailNotificationsHelp')}</p>
              </div>
              <Switch id="emailNotifications" defaultChecked />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="pushNotifications" className="font-medium">{tOwnerSettings('pushNotifications')}</Label>
                <p className="text-sm text-muted-foreground">{tOwnerSettings('pushNotificationsHelp')}</p>
              </div>
              <Switch id="pushNotifications" />
            </div>
             <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="inAppNotifications" className="font-medium">{tOwnerSettings('inAppNotifications')}</Label>
                <p className="text-sm text-muted-foreground">{tOwnerSettings('inAppNotificationsHelp')}</p>
              </div>
              <Switch id="inAppNotifications" defaultChecked/>
            </div>
          </CardContent>
        </Card>
        
        <div className="flex justify-end">
            <Button className="bg-accent hover:bg-accent/90 text-accent-foreground">
                <Save className="mr-2 h-4 w-4 rtl:ml-2 rtl:mr-0" /> {tOwnerSettings('saveAllSettings')}
            </Button>
        </div>
      </div>
    </AuthenticatedLayout>
  );
}
