'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Checkbox } from '@/components/ui/checkbox';
import { Plus, Minus, Search, Package } from 'lucide-react';
import { useScopedI18n } from '@/locales/client';
import { useToast } from '@/hooks/use-toast';
import { getMockOrders } from '@/lib/mock-order-data';
import { addMockReturn, generateReturnId } from '@/lib/mock-returns-data';
import type { Return, ReturnItem, Order } from '@/types';

interface NewReturnFormProps {
  onCancel: () => void;
  onSuccess: (newReturn: Return) => void;
}

interface SelectedItem {
  productId: string;
  productName: string;
  originalQuantity: number;
  returnQuantity: number;
  originalPrice: number;
  condition: 'excellent' | 'good' | 'damaged' | 'defective';
  reason: string;
}

export function NewReturnForm({ onCancel, onSuccess }: NewReturnFormProps) {
  const t = useScopedI18n('employeeReturnsManagement');
  const { toast } = useToast();

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [selectedItems, setSelectedItems] = useState<SelectedItem[]>([]);
  const [returnReason, setReturnReason] = useState<Return['returnReason']>('defective');
  const [returnReasonDetails, setReturnReasonDetails] = useState('');
  const [refundMethod, setRefundMethod] = useState<Return['refundMethod']>('cash');
  const [notes, setNotes] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [orders, setOrders] = useState<Order[]>([]);

  useEffect(() => {
    // Load orders
    const mockOrders = getMockOrders();
    // Filter delivered orders only
    const deliveredOrders = mockOrders.filter(order => order.status === 'delivered');
    setOrders(deliveredOrders);
  }, []);

  const filteredOrders = orders.filter(order => 
    order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
    order.customerName?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleOrderSelect = (order: Order) => {
    setSelectedOrder(order);
    // Initialize selected items with all order items
    const items: SelectedItem[] = order.items.map(item => ({
      productId: item.productId,
      productName: item.productName,
      originalQuantity: item.quantity,
      returnQuantity: 0,
      originalPrice: item.price,
      condition: 'good' as const,
      reason: ''
    }));
    setSelectedItems(items);
  };

  const handleItemQuantityChange = (productId: string, quantity: number) => {
    setSelectedItems(prev => prev.map(item => 
      item.productId === productId 
        ? { ...item, returnQuantity: Math.max(0, Math.min(quantity, item.originalQuantity)) }
        : item
    ));
  };

  const handleItemConditionChange = (productId: string, condition: SelectedItem['condition']) => {
    setSelectedItems(prev => prev.map(item => 
      item.productId === productId ? { ...item, condition } : item
    ));
  };

  const handleItemReasonChange = (productId: string, reason: string) => {
    setSelectedItems(prev => prev.map(item => 
      item.productId === productId ? { ...item, reason } : item
    ));
  };

  const getReasonText = (reason: Return['returnReason']) => {
    switch (reason) {
      case 'defective': return t('reasonDefective');
      case 'wrong_item': return t('reasonWrongItem');
      case 'not_satisfied': return t('reasonNotSatisfied');
      case 'damaged_shipping': return t('reasonDamagedShipping');
      case 'expired': return t('reasonExpired');
      case 'other': return t('reasonOther');
      default: return reason;
    }
  };

  const getRefundMethodText = (method: Return['refundMethod']) => {
    switch (method) {
      case 'cash': return t('refundCash');
      case 'credit': return t('refundCredit');
      case 'exchange': return t('refundExchange');
      case 'store_credit': return t('refundStoreCredit');
      default: return method;
    }
  };

  const getConditionText = (condition: SelectedItem['condition']) => {
    switch (condition) {
      case 'excellent': return t('conditionExcellent');
      case 'good': return t('conditionGood');
      case 'damaged': return t('conditionDamaged');
      case 'defective': return t('conditionDefective');
      default: return condition;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-YE', {
      style: 'currency',
      currency: 'YER',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const itemsToReturn = selectedItems.filter(item => item.returnQuantity > 0);
  const totalReturnAmount = itemsToReturn.reduce((sum, item) => 
    sum + (item.returnQuantity * item.originalPrice), 0
  );

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedOrder) {
      toast({
        title: t('errorCreatingReturn'),
        description: 'يرجى اختيار طلب أولاً',
        variant: 'destructive',
      });
      return;
    }

    if (itemsToReturn.length === 0) {
      toast({
        title: t('errorCreatingReturn'),
        description: 'يرجى اختيار منتج واحد على الأقل للإرجاع',
        variant: 'destructive',
      });
      return;
    }

    // Validate that all items have reasons
    const invalidItems = itemsToReturn.filter(item => !item.reason.trim());
    if (invalidItems.length > 0) {
      toast({
        title: t('errorCreatingReturn'),
        description: 'يرجى إدخال سبب الإرجاع لجميع المنتجات',
        variant: 'destructive',
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Create return items
      const returnItems: ReturnItem[] = itemsToReturn.map((item, index) => ({
        id: `ret_item_${Date.now()}_${index}`,
        productId: item.productId,
        productName: item.productName,
        originalQuantity: item.originalQuantity,
        returnQuantity: item.returnQuantity,
        originalPrice: item.originalPrice,
        returnAmount: item.returnQuantity * item.originalPrice,
        condition: item.condition,
        reason: item.reason
      }));

      // Create new return
      const newReturn: Return = {
        id: generateReturnId(),
        orderId: selectedOrder.id,
        customerId: selectedOrder.customerId,
        customerName: selectedOrder.customerName || 'عميل غير محدد',
        items: returnItems,
        totalReturnAmount,
        returnReason,
        returnReasonDetails: returnReasonDetails || undefined,
        status: 'pending',
        refundMethod,
        createdAt: new Date().toISOString(),
        notes: notes || undefined
      };

      // Save to mock data
      addMockReturn(newReturn);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      toast({
        title: t('returnCreatedSuccess'),
        description: `تم إنشاء المرتجع ${newReturn.id} بنجاح`,
      });

      onSuccess(newReturn);

    } catch (error) {
      toast({
        title: t('errorCreatingReturn'),
        description: 'حدث خطأ أثناء إنشاء المرتجع. يرجى المحاولة مرة أخرى.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            {t('createNewReturn')}
          </CardTitle>
          <CardDescription>
            إنشاء مرتجع جديد للعميل
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Order Selection */}
            <div className="space-y-4">
              <Label>{t('selectOrder')}</Label>
              <div className="space-y-3">
                <Input
                  placeholder="البحث برقم الطلب أو اسم العميل..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
                
                {selectedOrder ? (
                  <div className="p-4 border rounded-lg bg-muted">
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="font-medium">الطلب: {selectedOrder.id}</p>
                        <p className="text-sm text-muted-foreground">العميل: {selectedOrder.customerName}</p>
                        <p className="text-sm text-muted-foreground">
                          التاريخ: {new Date(selectedOrder.createdAt).toLocaleDateString('ar-YE')}
                        </p>
                      </div>
                      <Button 
                        type="button" 
                        variant="outline" 
                        size="sm"
                        onClick={() => {
                          setSelectedOrder(null);
                          setSelectedItems([]);
                        }}
                      >
                        تغيير
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="max-h-48 overflow-y-auto border rounded-lg">
                    {filteredOrders.length === 0 ? (
                      <div className="p-4 text-center text-muted-foreground">
                        {searchTerm ? 'لا توجد طلبات تطابق البحث' : 'لا توجد طلبات مكتملة'}
                      </div>
                    ) : (
                      <div className="space-y-1">
                        {filteredOrders.map(order => (
                          <div
                            key={order.id}
                            className="p-3 hover:bg-muted cursor-pointer border-b last:border-b-0"
                            onClick={() => handleOrderSelect(order)}
                          >
                            <div className="flex justify-between items-center">
                              <div>
                                <p className="font-medium">{order.id}</p>
                                <p className="text-sm text-muted-foreground">{order.customerName}</p>
                              </div>
                              <div className="text-right">
                                <p className="text-sm">{formatCurrency(order.totalAmount)}</p>
                                <p className="text-xs text-muted-foreground">
                                  {new Date(order.createdAt).toLocaleDateString('ar-YE')}
                                </p>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>

            {selectedOrder && (
              <>
                <Separator />
                
                {/* Items Selection */}
                <div className="space-y-4">
                  <Label>{t('selectProducts')}</Label>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>{t('productName')}</TableHead>
                        <TableHead>{t('originalQuantity')}</TableHead>
                        <TableHead>{t('returnQuantity')}</TableHead>
                        <TableHead>{t('condition')}</TableHead>
                        <TableHead>{t('itemReason')}</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {selectedItems.map((item) => (
                        <TableRow key={item.productId}>
                          <TableCell className="font-medium">{item.productName}</TableCell>
                          <TableCell>{item.originalQuantity}</TableCell>
                          <TableCell>
                            <Input
                              type="number"
                              min="0"
                              max={item.originalQuantity}
                              value={item.returnQuantity}
                              onChange={(e) => handleItemQuantityChange(item.productId, parseInt(e.target.value) || 0)}
                              className="w-20"
                            />
                          </TableCell>
                          <TableCell>
                            <Select 
                              value={item.condition} 
                              onValueChange={(value: SelectedItem['condition']) => 
                                handleItemConditionChange(item.productId, value)
                              }
                            >
                              <SelectTrigger className="w-32">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="excellent">{getConditionText('excellent')}</SelectItem>
                                <SelectItem value="good">{getConditionText('good')}</SelectItem>
                                <SelectItem value="damaged">{getConditionText('damaged')}</SelectItem>
                                <SelectItem value="defective">{getConditionText('defective')}</SelectItem>
                              </SelectContent>
                            </Select>
                          </TableCell>
                          <TableCell>
                            <Input
                              placeholder="سبب الإرجاع..."
                              value={item.reason}
                              onChange={(e) => handleItemReasonChange(item.productId, e.target.value)}
                              className="w-48"
                            />
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                <Separator />

                {/* Return Details */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>{t('returnReason')}</Label>
                    <Select value={returnReason} onValueChange={(value: Return['returnReason']) => setReturnReason(value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="defective">{getReasonText('defective')}</SelectItem>
                        <SelectItem value="wrong_item">{getReasonText('wrong_item')}</SelectItem>
                        <SelectItem value="not_satisfied">{getReasonText('not_satisfied')}</SelectItem>
                        <SelectItem value="damaged_shipping">{getReasonText('damaged_shipping')}</SelectItem>
                        <SelectItem value="expired">{getReasonText('expired')}</SelectItem>
                        <SelectItem value="other">{getReasonText('other')}</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>{t('refundMethod')}</Label>
                    <Select value={refundMethod} onValueChange={(value: Return['refundMethod']) => setRefundMethod(value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="cash">{getRefundMethodText('cash')}</SelectItem>
                        <SelectItem value="credit">{getRefundMethodText('credit')}</SelectItem>
                        <SelectItem value="exchange">{getRefundMethodText('exchange')}</SelectItem>
                        <SelectItem value="store_credit">{getRefundMethodText('store_credit')}</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>{t('returnReasonDetails')}</Label>
                  <Textarea
                    value={returnReasonDetails}
                    onChange={(e) => setReturnReasonDetails(e.target.value)}
                    placeholder="تفاصيل إضافية حول سبب الإرجاع..."
                    rows={3}
                  />
                </div>

                <div className="space-y-2">
                  <Label>{t('additionalNotes')}</Label>
                  <Textarea
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    placeholder="ملاحظات إضافية..."
                    rows={2}
                  />
                </div>

                {/* Summary */}
                {itemsToReturn.length > 0 && (
                  <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <h4 className="font-medium mb-2">ملخص المرتجع</h4>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span>عدد المنتجات:</span>
                        <span>{itemsToReturn.length}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>إجمالي الكمية:</span>
                        <span>{itemsToReturn.reduce((sum, item) => sum + item.returnQuantity, 0)}</span>
                      </div>
                      <div className="flex justify-between font-medium">
                        <span>المبلغ الإجمالي:</span>
                        <span>{formatCurrency(totalReturnAmount)}</span>
                      </div>
                    </div>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex gap-3 pt-4">
                  <Button
                    type="submit"
                    disabled={isSubmitting || itemsToReturn.length === 0}
                    className="flex-1"
                  >
                    {isSubmitting ? 'جارٍ الإنشاء...' : t('submitReturn')}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={onCancel}
                    disabled={isSubmitting}
                  >
                    {t('cancel')}
                  </Button>
                </div>
              </>
            )}
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
