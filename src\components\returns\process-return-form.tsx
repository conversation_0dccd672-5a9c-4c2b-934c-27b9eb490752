'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { AlertTriangle, CheckCircle, XCircle, Calculator } from 'lucide-react';
import { useScopedI18n } from '@/locales/client';
import { useToast } from '@/hooks/use-toast';
import type { Return } from '@/types';

interface ProcessReturnFormProps {
  returnData: Return;
  onCancel: () => void;
  onSuccess: (action: 'approve' | 'reject' | 'process', updatedReturn: Return) => void;
}

export function ProcessReturnForm({ returnData, onCancel, onSuccess }: ProcessReturnFormProps) {
  const t = useScopedI18n('employeeReturnsManagement');
  const { toast } = useToast();
  
  const [action, setAction] = useState<'approve' | 'reject' | 'process'>('approve');
  const [refundAmount, setRefundAmount] = useState(returnData.totalReturnAmount);
  const [restockingFee, setRestockingFee] = useState(0);
  const [notes, setNotes] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);

  const finalRefundAmount = refundAmount - (refundAmount * restockingFee / 100);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-YE', {
      style: 'currency',
      currency: 'YER',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getRefundMethodText = (method: Return['refundMethod']) => {
    switch (method) {
      case 'cash': return t('refundCash');
      case 'credit': return t('refundCredit');
      case 'exchange': return t('refundExchange');
      case 'store_credit': return t('refundStoreCredit');
      default: return method;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsProcessing(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const updatedReturn: Return = {
        ...returnData,
        status: action === 'reject' ? 'rejected' : action === 'approve' ? 'approved' : 'processed',
        processedAt: new Date().toISOString(),
        processedBy: 'employee001', // This would come from auth context
        processedByName: 'أحمد (موظف الوفاء)', // This would come from auth context
        refundAmount: action === 'reject' ? 0 : finalRefundAmount,
        notes: notes || undefined
      };

      onSuccess(action, updatedReturn);

      const actionText = action === 'approve' ? t('returnApprovedSuccess') : 
                        action === 'reject' ? t('returnRejectedSuccess') : 
                        t('returnProcessedSuccess');

      toast({
        title: actionText,
        description: `تم ${action === 'approve' ? 'الموافقة على' : action === 'reject' ? 'رفض' : 'معالجة'} المرتجع ${returnData.id}`,
      });

    } catch (error) {
      toast({
        title: t('errorProcessingReturn'),
        description: 'حدث خطأ أثناء معالجة المرتجع. يرجى المحاولة مرة أخرى.',
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {action === 'approve' && <CheckCircle className="h-5 w-5 text-green-600" />}
            {action === 'reject' && <XCircle className="h-5 w-5 text-red-600" />}
            {action === 'process' && <Calculator className="h-5 w-5 text-blue-600" />}
            {t('processReturn')} - {returnData.id}
          </CardTitle>
          <CardDescription>
            اختر الإجراء المناسب لهذا المرتجع وأدخل التفاصيل المطلوبة
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Action Selection */}
            <div className="space-y-3">
              <Label>نوع الإجراء</Label>
              <div className="flex gap-3">
                <Button
                  type="button"
                  variant={action === 'approve' ? 'default' : 'outline'}
                  onClick={() => setAction('approve')}
                  className="flex-1"
                >
                  <CheckCircle className="h-4 w-4 mr-2" />
                  {t('approve')}
                </Button>
                <Button
                  type="button"
                  variant={action === 'reject' ? 'destructive' : 'outline'}
                  onClick={() => setAction('reject')}
                  className="flex-1"
                >
                  <XCircle className="h-4 w-4 mr-2" />
                  {t('reject')}
                </Button>
                <Button
                  type="button"
                  variant={action === 'process' ? 'default' : 'outline'}
                  onClick={() => setAction('process')}
                  className="flex-1"
                >
                  <Calculator className="h-4 w-4 mr-2" />
                  {t('process')}
                </Button>
              </div>
            </div>

            <Separator />

            {/* Return Summary */}
            <div className="grid grid-cols-2 gap-4 p-4 bg-muted rounded-lg">
              <div>
                <Label className="text-sm text-muted-foreground">العميل</Label>
                <p className="font-medium">{returnData.customerName}</p>
              </div>
              <div>
                <Label className="text-sm text-muted-foreground">رقم الطلب</Label>
                <p className="font-medium">{returnData.orderId}</p>
              </div>
              <div>
                <Label className="text-sm text-muted-foreground">المبلغ الأصلي</Label>
                <p className="font-medium">{formatCurrency(returnData.totalReturnAmount)}</p>
              </div>
              <div>
                <Label className="text-sm text-muted-foreground">طريقة الاسترداد</Label>
                <Badge variant="outline">{getRefundMethodText(returnData.refundMethod)}</Badge>
              </div>
            </div>

            {/* Refund Calculation (only for approve/process) */}
            {action !== 'reject' && (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="refundAmount">{t('refundAmount')} (ر.ي)</Label>
                    <Input
                      id="refundAmount"
                      type="number"
                      value={refundAmount}
                      onChange={(e) => setRefundAmount(Number(e.target.value))}
                      min="0"
                      max={returnData.totalReturnAmount}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="restockingFee">{t('restockingFee')} (%)</Label>
                    <Input
                      id="restockingFee"
                      type="number"
                      value={restockingFee}
                      onChange={(e) => setRestockingFee(Number(e.target.value))}
                      min="0"
                      max="100"
                      step="0.1"
                    />
                  </div>
                </div>

                {/* Final Amount Calculation */}
                <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="flex justify-between items-center">
                    <span className="font-medium">المبلغ الأصلي:</span>
                    <span>{formatCurrency(refundAmount)}</span>
                  </div>
                  {restockingFee > 0 && (
                    <div className="flex justify-between items-center text-sm text-muted-foreground">
                      <span>رسوم إعادة التخزين ({restockingFee}%):</span>
                      <span>-{formatCurrency(refundAmount * restockingFee / 100)}</span>
                    </div>
                  )}
                  <Separator className="my-2" />
                  <div className="flex justify-between items-center font-bold text-lg">
                    <span>{t('finalRefundAmount')}:</span>
                    <span className="text-green-600">{formatCurrency(finalRefundAmount)}</span>
                  </div>
                </div>
              </div>
            )}

            {/* Processing Notes */}
            <div className="space-y-2">
              <Label htmlFor="notes">{t('processingNotes')}</Label>
              <Textarea
                id="notes"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                placeholder="أدخل أي ملاحظات حول معالجة هذا المرتجع..."
                rows={3}
              />
            </div>

            {/* Warning for rejection */}
            {action === 'reject' && (
              <div className="flex items-start gap-3 p-4 bg-red-50 border border-red-200 rounded-lg">
                <AlertTriangle className="h-5 w-5 text-red-600 mt-0.5" />
                <div className="text-sm">
                  <p className="font-medium text-red-800">تحذير: رفض المرتجع</p>
                  <p className="text-red-700">
                    سيتم رفض هذا المرتجع ولن يحصل العميل على أي استرداد. تأكد من صحة هذا القرار.
                  </p>
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex gap-3 pt-4">
              <Button
                type="submit"
                disabled={isProcessing}
                className="flex-1"
                variant={action === 'reject' ? 'destructive' : 'default'}
              >
                {isProcessing ? 'جارٍ المعالجة...' : t('confirmAction')}
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isProcessing}
              >
                {t('cancel')}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
