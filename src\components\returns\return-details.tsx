'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Package, User, Calendar, CreditCard, FileText, ArrowLeft } from 'lucide-react';
import { useScopedI18n } from '@/locales/client';
import type { Return } from '@/types';

interface ReturnDetailsProps {
  returnData: Return;
  onBack: () => void;
  onProcess?: () => void;
  showProcessButton?: boolean;
}

export function ReturnDetails({ returnData, onBack, onProcess, showProcessButton = true }: ReturnDetailsProps) {
  const t = useScopedI18n('employeeReturnsManagement');

  const getStatusBadgeVariant = (status: Return['status']) => {
    switch (status) {
      case 'pending': return 'secondary';
      case 'approved': return 'default';
      case 'processed': return 'outline';
      case 'refunded': return 'default';
      case 'rejected': return 'destructive';
      default: return 'secondary';
    }
  };

  const getStatusText = (status: Return['status']) => {
    switch (status) {
      case 'pending': return t('statusPending');
      case 'approved': return t('statusApproved');
      case 'processed': return t('statusProcessed');
      case 'refunded': return t('statusRefunded');
      case 'rejected': return t('statusRejected');
      default: return status;
    }
  };

  const getReasonText = (reason: Return['returnReason']) => {
    switch (reason) {
      case 'defective': return t('reasonDefective');
      case 'wrong_item': return t('reasonWrongItem');
      case 'not_satisfied': return t('reasonNotSatisfied');
      case 'damaged_shipping': return t('reasonDamagedShipping');
      case 'expired': return t('reasonExpired');
      case 'other': return t('reasonOther');
      default: return reason;
    }
  };

  const getRefundMethodText = (method: Return['refundMethod']) => {
    switch (method) {
      case 'cash': return t('refundCash');
      case 'credit': return t('refundCredit');
      case 'exchange': return t('refundExchange');
      case 'store_credit': return t('refundStoreCredit');
      default: return method;
    }
  };

  const getConditionText = (condition: string) => {
    switch (condition) {
      case 'excellent': return t('conditionExcellent');
      case 'good': return t('conditionGood');
      case 'damaged': return t('conditionDamaged');
      case 'defective': return t('conditionDefective');
      default: return condition;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-YE', {
      style: 'currency',
      currency: 'YER',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-YE', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={onBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            العودة
          </Button>
          <div>
            <h1 className="text-2xl font-bold">{t('returnDetails')} - {returnData.id}</h1>
            <p className="text-muted-foreground">
              {formatDate(returnData.createdAt)}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-4">
          <Badge variant={getStatusBadgeVariant(returnData.status)} className="text-sm">
            {getStatusText(returnData.status)}
          </Badge>
          {showProcessButton && onProcess && returnData.status === 'pending' && (
            <Button onClick={onProcess}>
              {t('process')}
            </Button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Customer Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              {t('customerInfo')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <label className="text-sm font-medium text-muted-foreground">{t('customerName')}</label>
              <p className="text-sm">{returnData.customerName}</p>
            </div>
            {returnData.customerPhone && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">رقم الهاتف</label>
                <p className="text-sm">{returnData.customerPhone}</p>
              </div>
            )}
            <div>
              <label className="text-sm font-medium text-muted-foreground">رقم العميل</label>
              <p className="text-sm">{returnData.customerId}</p>
            </div>
          </CardContent>
        </Card>

        {/* Order Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              {t('orderInfo')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <label className="text-sm font-medium text-muted-foreground">{t('orderId')}</label>
              <p className="text-sm">{returnData.orderId}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">{t('returnDate')}</label>
              <p className="text-sm">{formatDate(returnData.createdAt)}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">{t('totalAmount')}</label>
              <p className="text-sm font-semibold">{formatCurrency(returnData.totalReturnAmount)}</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Return Reason */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            {t('returnReason')}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div>
            <label className="text-sm font-medium text-muted-foreground">السبب الرئيسي</label>
            <p className="text-sm">{getReasonText(returnData.returnReason)}</p>
          </div>
          {returnData.returnReasonDetails && (
            <div>
              <label className="text-sm font-medium text-muted-foreground">{t('returnReasonDetails')}</label>
              <p className="text-sm">{returnData.returnReasonDetails}</p>
            </div>
          )}
          <div>
            <label className="text-sm font-medium text-muted-foreground">{t('refundMethod')}</label>
            <p className="text-sm">{getRefundMethodText(returnData.refundMethod)}</p>
          </div>
        </CardContent>
      </Card>

      {/* Returned Items */}
      <Card>
        <CardHeader>
          <CardTitle>{t('returnItems')}</CardTitle>
          <CardDescription>
            المنتجات المطلوب إرجاعها وحالتها
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{t('productName')}</TableHead>
                <TableHead>{t('originalQuantity')}</TableHead>
                <TableHead>{t('returnQuantity')}</TableHead>
                <TableHead>{t('originalPrice')}</TableHead>
                <TableHead>{t('returnAmount')}</TableHead>
                <TableHead>{t('condition')}</TableHead>
                <TableHead>{t('itemReason')}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {returnData.items.map((item) => (
                <TableRow key={item.id}>
                  <TableCell className="font-medium">{item.productName}</TableCell>
                  <TableCell>{item.originalQuantity}</TableCell>
                  <TableCell>{item.returnQuantity}</TableCell>
                  <TableCell>{formatCurrency(item.originalPrice)}</TableCell>
                  <TableCell>{formatCurrency(item.returnAmount)}</TableCell>
                  <TableCell>
                    <Badge variant="outline">
                      {getConditionText(item.condition)}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-sm">{item.reason}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Exchange Items (if applicable) */}
      {returnData.exchangeItems && returnData.exchangeItems.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>{t('exchangeItems')}</CardTitle>
            <CardDescription>
              المنتجات المطلوب استبدالها
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t('productName')}</TableHead>
                  <TableHead>{t('exchangeQuantity')}</TableHead>
                  <TableHead>{t('exchangePrice')}</TableHead>
                  <TableHead>المبلغ الإجمالي</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {returnData.exchangeItems.map((item, index) => (
                  <TableRow key={index}>
                    <TableCell className="font-medium">{item.productName}</TableCell>
                    <TableCell>{item.quantity}</TableCell>
                    <TableCell>{formatCurrency(item.price)}</TableCell>
                    <TableCell>{formatCurrency(item.price * item.quantity)}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}

      {/* Processing Information */}
      {(returnData.processedBy || returnData.approvedBy || returnData.notes) && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              معلومات المعالجة
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {returnData.processedBy && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">{t('processedBy')}</label>
                <p className="text-sm">{returnData.processedByName || returnData.processedBy}</p>
              </div>
            )}
            {returnData.processedAt && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">{t('processedAt')}</label>
                <p className="text-sm">{formatDate(returnData.processedAt)}</p>
              </div>
            )}
            {returnData.approvedBy && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">{t('approvedBy')}</label>
                <p className="text-sm">{returnData.approvedByName || returnData.approvedBy}</p>
              </div>
            )}
            {returnData.refundAmount && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">{t('finalRefundAmount')}</label>
                <p className="text-sm font-semibold">{formatCurrency(returnData.refundAmount)}</p>
              </div>
            )}
            {returnData.notes && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">{t('notes')}</label>
                <p className="text-sm">{returnData.notes}</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
